"""
Debug script to inspect GMX registration form structure
"""

import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.dolphin_api import DolphinAPIClient
from src.dolphin_driver import DolphinWebDriver
from src.utils import setup_logging
from config.settings import settings


def debug_gmx_form():
    """Debug the GMX registration form structure"""
    
    setup_logging(log_level="INFO")
    
    print("🔍 GMX Form Structure Debug Tool")
    print("=" * 50)
    
    # Initialize components
    dolphin_api = DolphinAPIClient(
        api_url=settings.dolphin.api_url,
        api_token=settings.dolphin.api_token
    )
    
    if not dolphin_api.health_check():
        print("❌ Cannot connect to Dolphin API")
        return
    
    if settings.dolphin.api_token and not dolphin_api.authenticate():
        print("❌ Authentication failed")
        return
    
    # Initialize WebDriver
    dolphin_driver = DolphinWebDriver(
        profile_id=settings.dolphin.profile_id,
        chromedriver_path=settings.chromedriver.chromedriver_path,
        dolphin_api=dolphin_api,
        headless=False,  # Keep visible for debugging
        implicit_wait=settings.automation.implicit_wait,
        page_load_timeout=settings.automation.page_load_timeout,
        script_timeout=settings.automation.script_timeout
    )
    
    try:
        # Start driver
        if not dolphin_driver.start():
            print("❌ Failed to start WebDriver")
            return
        
        driver = dolphin_driver.get_driver()
        
        # Navigate to GMX registration
        print(f"🌐 Navigating to: {settings.gmx.registration_url}")
        driver.get(settings.gmx.registration_url)
        time.sleep(5)  # Wait for page to load
        
        print("📋 Analyzing form structure...")
        
        # Find all input fields
        inputs = driver.find_elements("css selector", "input")
        print(f"\n📝 Found {len(inputs)} input fields:")
        for i, input_elem in enumerate(inputs):
            try:
                name = input_elem.get_attribute("name") or "N/A"
                id_attr = input_elem.get_attribute("id") or "N/A"
                type_attr = input_elem.get_attribute("type") or "N/A"
                placeholder = input_elem.get_attribute("placeholder") or "N/A"
                print(f"  {i+1}. name='{name}' id='{id_attr}' type='{type_attr}' placeholder='{placeholder}'")
            except:
                print(f"  {i+1}. [Could not read attributes]")
        
        # Find all buttons
        buttons = driver.find_elements("css selector", "button")
        print(f"\n🔘 Found {len(buttons)} buttons:")
        for i, button in enumerate(buttons):
            try:
                text = button.text or "N/A"
                type_attr = button.get_attribute("type") or "N/A"
                class_attr = button.get_attribute("class") or "N/A"
                print(f"  {i+1}. text='{text}' type='{type_attr}' class='{class_attr}'")
            except:
                print(f"  {i+1}. [Could not read attributes]")
        
        # Find all forms
        forms = driver.find_elements("css selector", "form")
        print(f"\n📄 Found {len(forms)} forms:")
        for i, form in enumerate(forms):
            try:
                action = form.get_attribute("action") or "N/A"
                method = form.get_attribute("method") or "N/A"
                print(f"  {i+1}. action='{action}' method='{method}'")
            except:
                print(f"  {i+1}. [Could not read attributes]")
        
        print("\n⏳ Keeping browser open for 30 seconds for manual inspection...")
        print("You can manually inspect the page structure in the browser.")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
    
    finally:
        print("🧹 Cleaning up...")
        dolphin_driver.cleanup()
        print("✅ Debug session completed")


if __name__ == "__main__":
    debug_gmx_form()
