"""
GMX Account Creator

This module handles the automation of GMX account creation using Dolphin{anty} profiles.
"""

import logging
import time
from typing import Optional, Dict, Any
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from src.dolphin_driver import DolphinWebDriver
from src.utils import PersonData, WaitStrategy, RetryHelper, FileHelper


class GMXCreatorError(Exception):
    """Custom exception for GMX Creator errors"""
    pass


class GMXAccountCreator:
    """Main class for creating GMX accounts"""
    
    def __init__(self, dolphin_driver: DolphinWebDriver, registration_url: str):
        """
        Initialize the GMX account creator
        
        Args:
            dolphin_driver: Dolphin WebDriver instance
            registration_url: GMX registration URL
        """
        self.driver = dolphin_driver
        self.registration_url = registration_url
        self.logger = logging.getLogger(__name__)
        
        # Common selectors for GMX registration form
        self.selectors = {
            # Personal information
            'first_name': 'input[name="firstName"]',
            'last_name': 'input[name="lastName"]',
            'email': 'input[name="email"]',
            'password': 'input[name="password"]',
            'password_confirm': 'input[name="passwordConfirm"]',
            
            # Birth date
            'birth_day': 'select[name="day"]',
            'birth_month': 'select[name="month"]',
            'birth_year': 'select[name="year"]',
            
            # Gender
            'gender_male': 'input[value="male"]',
            'gender_female': 'input[value="female"]',
            
            # Terms and conditions
            'terms_checkbox': 'input[name="terms"]',
            'privacy_checkbox': 'input[name="privacy"]',
            
            # Submit button
            'submit_button': 'button[type="submit"]',
            'create_account_button': 'input[value="Create Account"]',
            
            # Error messages
            'error_message': '.error-message',
            'field_error': '.field-error',
            
            # Success indicators
            'success_message': '.success-message',
            'welcome_message': '.welcome'
        }
    
    def create_account(self, person_data: PersonData, save_data: bool = True) -> Dict[str, Any]:
        """
        Create a GMX account with the provided person data
        
        Args:
            person_data: Person information for account creation
            save_data: Whether to save account data to file
            
        Returns:
            Dict containing creation result and details
        """
        result = {
            'success': False,
            'email': person_data.email,
            'error': None,
            'details': {}
        }
        
        try:
            self.logger.info(f"Starting GMX account creation for: {person_data.email}")
            
            # Navigate to registration page
            if not self._navigate_to_registration():
                result['error'] = "Failed to navigate to registration page"
                return result
            
            # Fill registration form
            if not self._fill_registration_form(person_data):
                result['error'] = "Failed to fill registration form"
                return result
            
            # Submit form
            if not self._submit_form():
                result['error'] = "Failed to submit registration form"
                return result
            
            # Check for success
            if self._check_registration_success():
                result['success'] = True
                result['details']['created_at'] = time.strftime('%Y-%m-%d %H:%M:%S')
                
                if save_data:
                    self._save_account_data(person_data)
                
                self.logger.info(f"Successfully created GMX account: {person_data.email}")
            else:
                error_msg = self._get_error_message()
                result['error'] = error_msg or "Registration failed - unknown error"
                self.logger.error(f"Registration failed for {person_data.email}: {result['error']}")
            
        except Exception as e:
            result['error'] = f"Unexpected error: {str(e)}"
            self.logger.error(f"Unexpected error creating account for {person_data.email}: {e}")
        
        return result
    
    def _navigate_to_registration(self) -> bool:
        """Navigate to GMX registration page"""
        try:
            driver = self.driver.get_driver()
            self.logger.info(f"Navigating to: {self.registration_url}")
            
            driver.get(self.registration_url)
            WaitStrategy.page_load_delay()
            
            # Wait for page to load and check if we're on the right page
            self.driver.wait_for_element(By.CSS_SELECTOR, 'form', timeout=15)
            
            return True
            
        except TimeoutException:
            self.logger.error("Timeout waiting for registration page to load")
            return False
        except Exception as e:
            self.logger.error(f"Error navigating to registration page: {e}")
            return False
    
    def _fill_registration_form(self, person_data: PersonData) -> bool:
        """Fill out the registration form with person data"""
        try:
            self.logger.info("Filling registration form")
            
            # Fill first name
            if not self.driver.safe_send_keys(By.CSS_SELECTOR, self.selectors['first_name'], person_data.first_name):
                return False
            WaitStrategy.form_fill_delay()
            
            # Fill last name
            if not self.driver.safe_send_keys(By.CSS_SELECTOR, self.selectors['last_name'], person_data.last_name):
                return False
            WaitStrategy.form_fill_delay()
            
            # Fill email
            if not self.driver.safe_send_keys(By.CSS_SELECTOR, self.selectors['email'], person_data.email):
                return False
            WaitStrategy.form_fill_delay()
            
            # Fill password
            if not self.driver.safe_send_keys(By.CSS_SELECTOR, self.selectors['password'], person_data.password):
                return False
            WaitStrategy.form_fill_delay()
            
            # Confirm password
            if not self.driver.safe_send_keys(By.CSS_SELECTOR, self.selectors['password_confirm'], person_data.password):
                return False
            WaitStrategy.form_fill_delay()
            
            # Fill birth date
            if not self._fill_birth_date(person_data):
                return False
            
            # Select gender (randomly)
            if not self._select_gender():
                return False
            
            # Accept terms and conditions
            if not self._accept_terms():
                return False
            
            self.logger.info("Successfully filled registration form")
            return True
            
        except Exception as e:
            self.logger.error(f"Error filling registration form: {e}")
            return False
    
    def _fill_birth_date(self, person_data: PersonData) -> bool:
        """Fill birth date fields"""
        try:
            driver = self.driver.get_driver()
            
            # Day
            day_select = Select(driver.find_element(By.CSS_SELECTOR, self.selectors['birth_day']))
            day_select.select_by_value(str(person_data.birth_day))
            WaitStrategy.form_fill_delay()
            
            # Month
            month_select = Select(driver.find_element(By.CSS_SELECTOR, self.selectors['birth_month']))
            month_select.select_by_value(str(person_data.birth_month))
            WaitStrategy.form_fill_delay()
            
            # Year
            year_select = Select(driver.find_element(By.CSS_SELECTOR, self.selectors['birth_year']))
            year_select.select_by_value(str(person_data.birth_year))
            WaitStrategy.form_fill_delay()
            
            return True
            
        except (NoSuchElementException, TimeoutException) as e:
            self.logger.error(f"Error filling birth date: {e}")
            return False
    
    def _select_gender(self) -> bool:
        """Select gender randomly"""
        try:
            import random
            
            # Randomly choose gender
            gender_selector = random.choice([self.selectors['gender_male'], self.selectors['gender_female']])
            
            if not self.driver.safe_click(By.CSS_SELECTOR, gender_selector):
                return False
            
            WaitStrategy.form_fill_delay()
            return True
            
        except Exception as e:
            self.logger.error(f"Error selecting gender: {e}")
            return False
    
    def _accept_terms(self) -> bool:
        """Accept terms and conditions"""
        try:
            # Terms checkbox
            if not self.driver.safe_click(By.CSS_SELECTOR, self.selectors['terms_checkbox']):
                self.logger.warning("Could not find terms checkbox")
            
            WaitStrategy.form_fill_delay()
            
            # Privacy checkbox (if exists)
            try:
                if not self.driver.safe_click(By.CSS_SELECTOR, self.selectors['privacy_checkbox']):
                    self.logger.warning("Could not find privacy checkbox")
            except:
                pass  # Privacy checkbox might not exist
            
            WaitStrategy.form_fill_delay()
            return True
            
        except Exception as e:
            self.logger.error(f"Error accepting terms: {e}")
            return False
    
    def _submit_form(self) -> bool:
        """Submit the registration form"""
        try:
            self.logger.info("Submitting registration form")
            
            # Try different submit button selectors
            submit_selectors = [
                self.selectors['submit_button'],
                self.selectors['create_account_button'],
                'button[type="submit"]',
                'input[type="submit"]',
                '.submit-btn',
                '.create-account'
            ]
            
            for selector in submit_selectors:
                if self.driver.safe_click(By.CSS_SELECTOR, selector, timeout=5):
                    self.logger.info(f"Clicked submit button with selector: {selector}")
                    WaitStrategy.page_load_delay()
                    return True
            
            self.logger.error("Could not find or click submit button")
            return False
            
        except Exception as e:
            self.logger.error(f"Error submitting form: {e}")
            return False
    
    def _check_registration_success(self) -> bool:
        """Check if registration was successful"""
        try:
            driver = self.driver.get_driver()
            
            # Wait for page to load after submission
            time.sleep(3)
            
            # Check for success indicators
            success_selectors = [
                self.selectors['success_message'],
                self.selectors['welcome_message'],
                '.success',
                '.confirmation',
                'h1:contains("Welcome")',
                'h2:contains("Success")'
            ]
            
            for selector in success_selectors:
                try:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        self.logger.info(f"Found success indicator: {selector}")
                        return True
                except:
                    continue
            
            # Check URL for success indicators
            current_url = driver.current_url.lower()
            success_url_indicators = ['success', 'welcome', 'confirmation', 'complete']
            
            for indicator in success_url_indicators:
                if indicator in current_url:
                    self.logger.info(f"URL indicates success: {current_url}")
                    return True
            
            # Check page title
            page_title = driver.title.lower()
            if any(word in page_title for word in ['welcome', 'success', 'confirmation']):
                self.logger.info(f"Page title indicates success: {page_title}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking registration success: {e}")
            return False
    
    def _get_error_message(self) -> Optional[str]:
        """Get error message from the page"""
        try:
            driver = self.driver.get_driver()
            
            error_selectors = [
                self.selectors['error_message'],
                self.selectors['field_error'],
                '.error',
                '.alert-error',
                '.validation-error'
            ]
            
            for selector in error_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text.strip():
                            return element.text.strip()
                except:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting error message: {e}")
            return None
    
    def _save_account_data(self, person_data: PersonData):
        """Save account data to file"""
        try:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"gmx_account_{person_data.email.replace('@', '_').replace('.', '_')}_{timestamp}.json"
            filepath = f"./data/accounts/{filename}"
            
            FileHelper.save_account_data(person_data, filepath)
            self.logger.info(f"Saved account data to: {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error saving account data: {e}")
