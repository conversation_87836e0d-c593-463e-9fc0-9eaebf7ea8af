"""
GMX Account Creator

This module handles the automation of GMX account creation using Dolphin{anty} profiles.
"""

import logging
import time
from typing import Optional, Dict, Any
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from src.dolphin_driver import DolphinWebDriver
from src.utils import PersonData, WaitStrategy, RetryHelper, FileHelper


class GMXCreatorError(Exception):
    """Custom exception for GMX Creator errors"""
    pass


class GMXAccountCreator:
    """Main class for creating GMX accounts"""
    
    def __init__(self, dolphin_driver: DolphinWebDriver, registration_url: str):
        """
        Initialize the GMX account creator
        
        Args:
            dolphin_driver: Dolphin WebDriver instance
            registration_url: GMX registration URL
        """
        self.driver = dolphin_driver
        self.registration_url = registration_url
        self.logger = logging.getLogger(__name__)
        
        # Common selectors for GMX registration form (multiple variations)
        self.selectors = {
            # Personal information - multiple possible selectors
            'first_name': [
                'input[name="firstName"]',
                'input[name="first_name"]',
                'input[name="firstname"]',
                'input[id="firstName"]',
                'input[id="first_name"]',
                'input[placeholder*="First"]',
                'input[placeholder*="first"]',
                'input[data-testid*="first"]'
            ],
            'last_name': [
                'input[name="lastName"]',
                'input[name="last_name"]',
                'input[name="lastname"]',
                'input[id="lastName"]',
                'input[id="last_name"]',
                'input[placeholder*="Last"]',
                'input[placeholder*="last"]',
                'input[data-testid*="last"]'
            ],
            'email': [
                'input[name="email"]',
                'input[name="emailAddress"]',
                'input[id="email"]',
                'input[type="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]'
            ],
            'password': [
                'input[name="password"]',
                'input[id="password"]',
                'input[type="password"]',
                'input[placeholder*="password"]',
                'input[placeholder*="Password"]'
            ],
            'password_confirm': [
                'input[name="passwordConfirm"]',
                'input[name="password_confirm"]',
                'input[name="confirmPassword"]',
                'input[name="confirm_password"]',
                'input[id="passwordConfirm"]',
                'input[id="confirmPassword"]'
            ],

            # Submit button variations
            'submit_buttons': [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Create")',
                'button:contains("Sign up")',
                'button:contains("Register")',
                '.submit-btn',
                '.create-account',
                '.signup-btn',
                '.register-btn'
            ]
        }
    
    def create_account(self, person_data: PersonData, save_data: bool = True) -> Dict[str, Any]:
        """
        Create a GMX account with the provided person data
        
        Args:
            person_data: Person information for account creation
            save_data: Whether to save account data to file
            
        Returns:
            Dict containing creation result and details
        """
        result = {
            'success': False,
            'email': person_data.email,
            'error': None,
            'details': {}
        }
        
        try:
            self.logger.info(f"Starting GMX account creation for: {person_data.email}")
            
            # Navigate to registration page
            if not self._navigate_to_registration():
                result['error'] = "Failed to navigate to registration page"
                return result
            
            # Fill registration form
            if not self._fill_registration_form(person_data):
                result['error'] = "Failed to fill registration form"
                return result
            
            # Submit form
            if not self._submit_form():
                result['error'] = "Failed to submit registration form"
                return result
            
            # Check for success
            if self._check_registration_success():
                result['success'] = True
                result['details']['created_at'] = time.strftime('%Y-%m-%d %H:%M:%S')
                
                if save_data:
                    self._save_account_data(person_data)
                
                self.logger.info(f"Successfully created GMX account: {person_data.email}")
            else:
                error_msg = self._get_error_message()
                result['error'] = error_msg or "Registration failed - unknown error"
                self.logger.error(f"Registration failed for {person_data.email}: {result['error']}")
            
        except Exception as e:
            result['error'] = f"Unexpected error: {str(e)}"
            self.logger.error(f"Unexpected error creating account for {person_data.email}: {e}")
        
        return result
    
    def _navigate_to_registration(self) -> bool:
        """Navigate to GMX registration page"""
        try:
            driver = self.driver.get_driver()
            self.logger.info(f"Navigating to: {self.registration_url}")
            
            driver.get(self.registration_url)
            WaitStrategy.page_load_delay()
            
            # Wait for page to load and check if we're on the right page
            self.driver.wait_for_element(By.CSS_SELECTOR, 'form', timeout=15)
            
            return True
            
        except TimeoutException:
            self.logger.error("Timeout waiting for registration page to load")
            return False
        except Exception as e:
            self.logger.error(f"Error navigating to registration page: {e}")
            return False
    
    def _find_element_by_selectors(self, selectors_list, timeout=10):
        """Try multiple selectors to find an element"""
        for selector in selectors_list:
            try:
                element = self.driver.wait_for_element(By.CSS_SELECTOR, selector, timeout=2)
                self.logger.debug(f"Found element with selector: {selector}")
                return element, selector
            except:
                continue
        return None, None

    def _fill_registration_form(self, person_data: PersonData) -> bool:
        """Fill out the registration form with person data"""
        try:
            self.logger.info("Filling registration form")

            # Fill first name
            element, selector = self._find_element_by_selectors(self.selectors['first_name'])
            if element:
                if not self.driver.safe_send_keys(By.CSS_SELECTOR, selector, person_data.first_name):
                    return False
                WaitStrategy.form_fill_delay()
            else:
                self.logger.warning("Could not find first name field")

            # Fill last name
            element, selector = self._find_element_by_selectors(self.selectors['last_name'])
            if element:
                if not self.driver.safe_send_keys(By.CSS_SELECTOR, selector, person_data.last_name):
                    return False
                WaitStrategy.form_fill_delay()
            else:
                self.logger.warning("Could not find last name field")

            # Fill email
            element, selector = self._find_element_by_selectors(self.selectors['email'])
            if element:
                if not self.driver.safe_send_keys(By.CSS_SELECTOR, selector, person_data.email):
                    return False
                WaitStrategy.form_fill_delay()
            else:
                self.logger.error("Could not find email field - this is required")
                return False

            # Fill password
            element, selector = self._find_element_by_selectors(self.selectors['password'])
            if element:
                if not self.driver.safe_send_keys(By.CSS_SELECTOR, selector, person_data.password):
                    return False
                WaitStrategy.form_fill_delay()
            else:
                self.logger.error("Could not find password field - this is required")
                return False

            # Confirm password (optional)
            element, selector = self._find_element_by_selectors(self.selectors['password_confirm'])
            if element:
                if not self.driver.safe_send_keys(By.CSS_SELECTOR, selector, person_data.password):
                    return False
                WaitStrategy.form_fill_delay()
            else:
                self.logger.warning("Could not find password confirmation field")

            # Try to fill additional fields (birth date, gender, terms) but don't fail if not found
            self._fill_optional_fields(person_data)

            self.logger.info("Successfully filled registration form")
            return True

        except Exception as e:
            self.logger.error(f"Error filling registration form: {e}")
            return False

    def _fill_optional_fields(self, person_data: PersonData):
        """Fill optional fields that may or may not be present"""
        try:
            # Try to fill birth date
            self._fill_birth_date(person_data)

            # Try to select gender
            self._select_gender()

            # Try to accept terms
            self._accept_terms()

        except Exception as e:
            self.logger.warning(f"Error filling optional fields: {e}")
    
    def _fill_birth_date(self, person_data: PersonData) -> bool:
        """Fill birth date fields"""
        try:
            driver = self.driver.get_driver()
            
            # Day
            day_select = Select(driver.find_element(By.CSS_SELECTOR, self.selectors['birth_day']))
            day_select.select_by_value(str(person_data.birth_day))
            WaitStrategy.form_fill_delay()
            
            # Month
            month_select = Select(driver.find_element(By.CSS_SELECTOR, self.selectors['birth_month']))
            month_select.select_by_value(str(person_data.birth_month))
            WaitStrategy.form_fill_delay()
            
            # Year
            year_select = Select(driver.find_element(By.CSS_SELECTOR, self.selectors['birth_year']))
            year_select.select_by_value(str(person_data.birth_year))
            WaitStrategy.form_fill_delay()
            
            return True
            
        except (NoSuchElementException, TimeoutException) as e:
            self.logger.error(f"Error filling birth date: {e}")
            return False
    
    def _select_gender(self) -> bool:
        """Select gender randomly"""
        try:
            import random
            
            # Randomly choose gender
            gender_selector = random.choice([self.selectors['gender_male'], self.selectors['gender_female']])
            
            if not self.driver.safe_click(By.CSS_SELECTOR, gender_selector):
                return False
            
            WaitStrategy.form_fill_delay()
            return True
            
        except Exception as e:
            self.logger.error(f"Error selecting gender: {e}")
            return False
    
    def _accept_terms(self) -> bool:
        """Accept terms and conditions"""
        try:
            # Terms checkbox
            if not self.driver.safe_click(By.CSS_SELECTOR, self.selectors['terms_checkbox']):
                self.logger.warning("Could not find terms checkbox")
            
            WaitStrategy.form_fill_delay()
            
            # Privacy checkbox (if exists)
            try:
                if not self.driver.safe_click(By.CSS_SELECTOR, self.selectors['privacy_checkbox']):
                    self.logger.warning("Could not find privacy checkbox")
            except:
                pass  # Privacy checkbox might not exist
            
            WaitStrategy.form_fill_delay()
            return True
            
        except Exception as e:
            self.logger.error(f"Error accepting terms: {e}")
            return False
    
    def _submit_form(self) -> bool:
        """Submit the registration form"""
        try:
            self.logger.info("Submitting registration form")

            # Try to find and click submit button using flexible selectors
            element, selector = self._find_element_by_selectors(self.selectors['submit_buttons'])
            if element and selector:
                if self.driver.safe_click(By.CSS_SELECTOR, selector, timeout=5):
                    self.logger.info(f"Clicked submit button with selector: {selector}")
                    WaitStrategy.page_load_delay()
                    return True

            # Fallback: try additional submit button selectors
            additional_selectors = [
                'button:contains("Create")',
                'button:contains("Sign up")',
                'button:contains("Register")',
                'a[href*="submit"]',
                '.btn-primary',
                '.btn-submit'
            ]

            for selector in additional_selectors:
                if self.driver.safe_click(By.CSS_SELECTOR, selector, timeout=2):
                    self.logger.info(f"Clicked submit button with fallback selector: {selector}")
                    WaitStrategy.page_load_delay()
                    return True

            self.logger.error("Could not find or click submit button")
            return False

        except Exception as e:
            self.logger.error(f"Error submitting form: {e}")
            return False
    
    def _check_registration_success(self) -> bool:
        """Check if registration was successful"""
        try:
            driver = self.driver.get_driver()
            
            # Wait for page to load after submission
            time.sleep(3)
            
            # Check for success indicators
            success_selectors = [
                self.selectors['success_message'],
                self.selectors['welcome_message'],
                '.success',
                '.confirmation',
                'h1:contains("Welcome")',
                'h2:contains("Success")'
            ]
            
            for selector in success_selectors:
                try:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        self.logger.info(f"Found success indicator: {selector}")
                        return True
                except:
                    continue
            
            # Check URL for success indicators
            current_url = driver.current_url.lower()
            success_url_indicators = ['success', 'welcome', 'confirmation', 'complete']
            
            for indicator in success_url_indicators:
                if indicator in current_url:
                    self.logger.info(f"URL indicates success: {current_url}")
                    return True
            
            # Check page title
            page_title = driver.title.lower()
            if any(word in page_title for word in ['welcome', 'success', 'confirmation']):
                self.logger.info(f"Page title indicates success: {page_title}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking registration success: {e}")
            return False
    
    def _get_error_message(self) -> Optional[str]:
        """Get error message from the page"""
        try:
            driver = self.driver.get_driver()
            
            error_selectors = [
                self.selectors['error_message'],
                self.selectors['field_error'],
                '.error',
                '.alert-error',
                '.validation-error'
            ]
            
            for selector in error_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text.strip():
                            return element.text.strip()
                except:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting error message: {e}")
            return None
    
    def _save_account_data(self, person_data: PersonData):
        """Save account data to file"""
        try:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"gmx_account_{person_data.email.replace('@', '_').replace('.', '_')}_{timestamp}.json"
            filepath = f"./data/accounts/{filename}"
            
            FileHelper.save_account_data(person_data, filepath)
            self.logger.info(f"Saved account data to: {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error saving account data: {e}")
