"""
Quick Start Script for <PERSON><PERSON>{anty} GMX Account Creator

This script helps users get started quickly by checking prerequisites
and guiding through the initial setup.
"""

import os
import sys
import subprocess
from pathlib import Path


def print_header():
    """Print welcome header"""
    print("=" * 60)
    print("🚀 Dolphin{anty} GMX Account Creator - Quick Start")
    print("=" * 60)
    print()


def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required. Current version:", sys.version)
        return False
    
    print(f"✅ Python {sys.version.split()[0]} is compatible")
    return True


def check_dependencies():
    """Check if dependencies are installed"""
    print("\n📦 Checking dependencies...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        # Try importing key dependencies
        import selenium
        import requests
        import pydantic
        print("✅ Core dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False


def check_chromedriver():
    """Check if ChromeDriver is available"""
    print("\n🌐 Checking ChromeDriver...")
    
    chromedriver_paths = [
        "./drivers/chromedriver.exe",
        "./drivers/chromedriver",
        "chromedriver.exe",
        "chromedriver"
    ]
    
    for path in chromedriver_paths:
        if os.path.exists(path):
            print(f"✅ ChromeDriver found at: {path}")
            return True
    
    print("❌ ChromeDriver not found")
    print("💡 Download from: https://anty-browser.s3.eu-central-1.amazonaws.com/chromedriver-138.zip")
    print("💡 Extract to: ./drivers/chromedriver.exe")
    return False


def check_env_file():
    """Check if .env file exists"""
    print("\n⚙️  Checking configuration...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file found")
        return True
    elif env_example.exists():
        print("⚠️  .env file not found, but .env.example exists")
        print("💡 Copy .env.example to .env and configure your settings")
        return False
    else:
        print("❌ No configuration files found")
        return False


def check_dolphin_anty():
    """Check if Dolphin{anty} is running"""
    print("\n🐬 Checking Dolphin{anty}...")
    
    try:
        import requests
        response = requests.get("http://localhost:3001/v1.0/browser_profiles", timeout=5)
        if response.status_code in [200, 401]:
            print("✅ Dolphin{anty} is running and accessible")
            return True
        else:
            print(f"⚠️  Dolphin{anty} responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Dolphin{anty}")
        print("💡 Make sure Dolphin{anty} is running")
        return False
    except Exception as e:
        print(f"❌ Error checking Dolphin{anty}: {e}")
        return False


def install_dependencies():
    """Install dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_env_file():
    """Create .env file from example"""
    print("\n⚙️  Creating .env file...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_example.exists():
        print("❌ .env.example not found")
        return False
    
    try:
        content = env_example.read_text()
        env_file.write_text(content)
        print("✅ .env file created from example")
        print("💡 Please edit .env file with your settings")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False


def run_test():
    """Run a basic test"""
    print("\n🧪 Running basic test...")
    
    try:
        # Add project root to path
        sys.path.insert(0, str(Path(".")))

        from config.settings import settings
        
        if settings.validate_all():
            print("✅ Configuration is valid")
            return True
        else:
            print("❌ Configuration validation failed")
            return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def main():
    """Main quick start function"""
    print_header()
    
    # Check prerequisites
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("ChromeDriver", check_chromedriver),
        ("Configuration", check_env_file),
        ("Dolphin{anty}", check_dolphin_anty),
    ]
    
    results = {}
    for name, check_func in checks:
        results[name] = check_func()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SETUP SUMMARY")
    print("=" * 60)
    
    all_good = True
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name:<20} {status}")
        if not result:
            all_good = False
    
    print()
    
    if all_good:
        print("🎉 All checks passed! You're ready to go!")
        print("\n🚀 Next steps:")
        print("1. Run: python src/main.py")
        print("2. Or try: python examples/basic_usage.py")
    else:
        print("⚠️  Some checks failed. Please fix the issues above.")
        
        # Offer to fix common issues
        if not results["Dependencies"]:
            fix = input("\n💡 Install dependencies now? (y/N): ").lower().strip()
            if fix == 'y':
                install_dependencies()
        
        if not results["Configuration"] and Path(".env.example").exists():
            fix = input("\n💡 Create .env file from example? (y/N): ").lower().strip()
            if fix == 'y':
                create_env_file()
    
    print("\n📚 For more information, see README.md")
    print("🆘 For support, check the troubleshooting section")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    input("\nPress Enter to exit...")
