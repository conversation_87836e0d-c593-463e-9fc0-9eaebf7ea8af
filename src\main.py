"""
Main Application Entry Point

This module provides the main entry point for the Dolphin{anty} GMX account creator.
"""

import sys
import logging
import traceback
from pathlib import Path
from typing import Optional

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.dolphin_api import Do<PERSON><PERSON><PERSON><PERSON>
from src.dolphin_driver import DolphinWebDriver
from src.gmx_creator import GMXAccountCreator
from src.utils import DataGenerator, setup_logging, FileHelper
from config.settings import settings


class GMXCreatorApp:
    """Main application class"""
    
    def __init__(self):
        """Initialize the application"""
        self.logger = None
        self.dolphin_api = None
        self.dolphin_driver = None
        self.gmx_creator = None
        self.data_generator = None
        
        # Initialize logging first
        self._setup_logging()
        
        # Validate settings
        if not settings.validate_all():
            self.logger.error("Settings validation failed")
            sys.exit(1)
        
        self.logger.info("GMX Creator Application initialized")
        self.logger.info(f"Settings summary: {settings.get_summary()}")
    
    def _setup_logging(self):
        """Setup application logging"""
        try:
            setup_logging(
                log_level=settings.logging.log_level,
                log_file=settings.logging.log_file,
                log_format=settings.logging.log_format
            )
            self.logger = logging.getLogger(__name__)
            self.logger.info("Logging initialized successfully")
        except Exception as e:
            print(f"Failed to setup logging: {e}")
            sys.exit(1)
    
    def initialize_components(self) -> bool:
        """Initialize all application components"""
        try:
            # Initialize Dolphin API client
            self.logger.info("Initializing Dolphin API client")
            self.dolphin_api = DolphinAPIClient(
                api_url=settings.dolphin.api_url,
                api_token=settings.dolphin.api_token
            )
            
            # Test API connection
            if not self.dolphin_api.health_check():
                self.logger.error("Dolphin API health check failed")
                return False
            
            # Authenticate if token is provided
            if settings.dolphin.api_token:
                if not self.dolphin_api.authenticate():
                    self.logger.error("Dolphin API authentication failed")
                    return False
            
            # Initialize Dolphin WebDriver
            self.logger.info("Initializing Dolphin WebDriver")
            if not settings.dolphin.profile_id:
                self.logger.error("No profile ID configured")
                return False
            
            self.dolphin_driver = DolphinWebDriver(
                profile_id=settings.dolphin.profile_id,
                chromedriver_path=settings.chromedriver.chromedriver_path,
                dolphin_api=self.dolphin_api,
                headless=settings.automation.headless,
                implicit_wait=settings.automation.implicit_wait,
                page_load_timeout=settings.automation.page_load_timeout,
                script_timeout=settings.automation.script_timeout
            )
            
            # Initialize GMX Creator
            self.logger.info("Initializing GMX Creator")
            self.gmx_creator = GMXAccountCreator(
                dolphin_driver=self.dolphin_driver,
                registration_url=settings.gmx.registration_url
            )
            
            # Initialize Data Generator
            self.logger.info("Initializing Data Generator")
            self.data_generator = DataGenerator(
                first_names=settings.account.first_names,
                last_names=settings.account.last_names
            )
            
            self.logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            self.logger.debug(traceback.format_exc())
            return False
    
    def create_single_account(self) -> bool:
        """Create a single GMX account"""
        try:
            # Generate person data
            self.logger.info("Generating person data")
            person_data = self.data_generator.generate_person(
                min_age=settings.account.min_age,
                max_age=settings.account.max_age
            )
            
            self.logger.info(f"Generated data for: {person_data.first_name} {person_data.last_name} ({person_data.email})")
            
            # Start Dolphin driver
            self.logger.info("Starting Dolphin WebDriver")
            if not self.dolphin_driver.start():
                self.logger.error("Failed to start Dolphin WebDriver")
                return False
            
            try:
                # Create account
                self.logger.info("Creating GMX account")
                result = self.gmx_creator.create_account(person_data, save_data=True)
                
                if result['success']:
                    self.logger.info(f"Successfully created account: {result['email']}")
                    return True
                else:
                    self.logger.error(f"Failed to create account: {result['error']}")
                    return False
                    
            finally:
                # Always cleanup
                self.logger.info("Cleaning up Dolphin WebDriver")
                self.dolphin_driver.cleanup()
            
        except Exception as e:
            self.logger.error(f"Error creating account: {e}")
            self.logger.debug(traceback.format_exc())
            return False
    
    def create_multiple_accounts(self, count: int, delay_between: int = 60) -> int:
        """
        Create multiple GMX accounts
        
        Args:
            count: Number of accounts to create
            delay_between: Delay between account creations in seconds
            
        Returns:
            int: Number of successfully created accounts
        """
        successful_count = 0
        
        self.logger.info(f"Starting creation of {count} accounts")
        
        for i in range(count):
            self.logger.info(f"Creating account {i + 1}/{count}")
            
            try:
                if self.create_single_account():
                    successful_count += 1
                    self.logger.info(f"Account {i + 1} created successfully")
                else:
                    self.logger.error(f"Failed to create account {i + 1}")
                
                # Delay between accounts (except for the last one)
                if i < count - 1:
                    self.logger.info(f"Waiting {delay_between} seconds before next account")
                    import time
                    time.sleep(delay_between)
                    
            except KeyboardInterrupt:
                self.logger.info("Account creation interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error creating account {i + 1}: {e}")
                continue
        
        self.logger.info(f"Account creation completed. Success: {successful_count}/{count}")
        return successful_count
    
    def run_interactive_mode(self):
        """Run the application in interactive mode"""
        self.logger.info("Starting interactive mode")
        
        while True:
            try:
                print("\n" + "="*50)
                print("Dolphin{anty} GMX Account Creator")
                print("="*50)
                print("1. Create single account")
                print("2. Create multiple accounts")
                print("3. Test Dolphin API connection")
                print("4. Show settings")
                print("5. Exit")
                print("-"*50)
                
                choice = input("Enter your choice (1-5): ").strip()
                
                if choice == '1':
                    print("\nCreating single account...")
                    success = self.create_single_account()
                    if success:
                        print("✅ Account created successfully!")
                    else:
                        print("❌ Failed to create account. Check logs for details.")
                
                elif choice == '2':
                    try:
                        count = int(input("Enter number of accounts to create: "))
                        if count <= 0:
                            print("❌ Number must be positive")
                            continue
                        
                        delay = int(input("Enter delay between accounts (seconds, default 60): ") or "60")
                        if delay < 0:
                            print("❌ Delay must be non-negative")
                            continue
                        
                        print(f"\nCreating {count} accounts with {delay}s delay...")
                        successful = self.create_multiple_accounts(count, delay)
                        print(f"✅ Created {successful}/{count} accounts successfully!")
                        
                    except ValueError:
                        print("❌ Invalid input. Please enter numbers only.")
                
                elif choice == '3':
                    print("\nTesting Dolphin API connection...")
                    if self.dolphin_api.health_check():
                        print("✅ Dolphin API is accessible")
                        if settings.dolphin.api_token:
                            if self.dolphin_api.authenticate():
                                print("✅ Authentication successful")
                            else:
                                print("❌ Authentication failed")
                    else:
                        print("❌ Dolphin API is not accessible")
                
                elif choice == '4':
                    print("\nCurrent Settings:")
                    print("-"*30)
                    summary = settings.get_summary()
                    for section, values in summary.items():
                        print(f"{section.upper()}:")
                        for key, value in values.items():
                            print(f"  {key}: {value}")
                        print()
                
                elif choice == '5':
                    print("Goodbye!")
                    break
                
                else:
                    print("❌ Invalid choice. Please enter 1-5.")
                    
            except KeyboardInterrupt:
                print("\n\nExiting...")
                break
            except Exception as e:
                self.logger.error(f"Error in interactive mode: {e}")
                print(f"❌ An error occurred: {e}")
    
    def cleanup(self):
        """Cleanup application resources"""
        try:
            if self.dolphin_driver:
                self.dolphin_driver.cleanup()
            self.logger.info("Application cleanup completed")
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error during cleanup: {e}")


def main():
    """Main entry point"""
    app = None
    
    try:
        # Create application instance
        app = GMXCreatorApp()
        
        # Initialize components
        if not app.initialize_components():
            print("❌ Failed to initialize application components")
            sys.exit(1)
        
        # Check command line arguments
        if len(sys.argv) > 1:
            if sys.argv[1] == '--single':
                # Create single account
                success = app.create_single_account()
                sys.exit(0 if success else 1)
            
            elif sys.argv[1] == '--multiple':
                # Create multiple accounts
                try:
                    count = int(sys.argv[2]) if len(sys.argv) > 2 else 1
                    delay = int(sys.argv[3]) if len(sys.argv) > 3 else 60
                    successful = app.create_multiple_accounts(count, delay)
                    sys.exit(0 if successful > 0 else 1)
                except (ValueError, IndexError):
                    print("Usage: python main.py --multiple <count> [delay_seconds]")
                    sys.exit(1)
            
            elif sys.argv[1] == '--help':
                print("Dolphin{anty} GMX Account Creator")
                print("Usage:")
                print("  python main.py                    # Interactive mode")
                print("  python main.py --single           # Create single account")
                print("  python main.py --multiple <count> [delay] # Create multiple accounts")
                print("  python main.py --help             # Show this help")
                sys.exit(0)
        
        # Default: run interactive mode
        app.run_interactive_mode()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        if app and app.logger:
            app.logger.critical(f"Fatal error: {e}")
            app.logger.debug(traceback.format_exc())
        sys.exit(1)
    finally:
        if app:
            app.cleanup()


if __name__ == "__main__":
    main()
