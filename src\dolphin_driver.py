"""
Dolphin{anty} WebDriver Wrapper

This module provides a custom WebDriver wrapper that connects to Dolphin{anty} profiles
using the custom ChromeDriver for undetected automation.
"""

import os
import time
import logging
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import WebDriverException, TimeoutException
from fake_useragent import UserAgent

from src.dolphin_api import DolphinAPIClient, ProfileStartResponse


class DolphinDriverError(Exception):
    """Custom exception for Dolphin Driver errors"""
    pass


class DolphinWebDriver:
    """Custom WebDriver wrapper for Dolphin{anty} profiles"""
    
    def __init__(self, 
                 profile_id: str,
                 chromedriver_path: str,
                 dolphin_api: DolphinAPIClient,
                 headless: bool = False,
                 implicit_wait: int = 10,
                 page_load_timeout: int = 30,
                 script_timeout: int = 30):
        """
        Initialize the Dolphin WebDriver
        
        Args:
            profile_id: Dolphin profile ID to use
            chromedriver_path: Path to the custom ChromeDriver executable
            dolphin_api: Dolphin API client instance
            headless: Whether to run in headless mode
            implicit_wait: Implicit wait timeout in seconds
            page_load_timeout: Page load timeout in seconds
            script_timeout: Script execution timeout in seconds
        """
        self.profile_id = profile_id
        self.chromedriver_path = chromedriver_path
        self.dolphin_api = dolphin_api
        self.headless = headless
        self.implicit_wait = implicit_wait
        self.page_load_timeout = page_load_timeout
        self.script_timeout = script_timeout
        
        self.driver: Optional[webdriver.Chrome] = None
        self.profile_data: Optional[ProfileStartResponse] = None
        self.logger = logging.getLogger(__name__)
        
        # Validate ChromeDriver path
        if not os.path.exists(chromedriver_path):
            raise DolphinDriverError(f"ChromeDriver not found at: {chromedriver_path}")
    
    def start(self) -> bool:
        """
        Start the Dolphin profile and connect WebDriver
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Start the Dolphin profile
            self.logger.info(f"Starting Dolphin profile: {self.profile_id}")
            self.profile_data = self.dolphin_api.start_profile(self.profile_id, self.headless)
            
            if not self.profile_data.success:
                raise DolphinDriverError(f"Failed to start profile: {self.profile_data.error}")
            
            # Wait a moment for the profile to fully initialize
            time.sleep(2)
            
            # Create WebDriver instance
            self._create_driver()
            
            # Configure timeouts
            self.driver.implicitly_wait(self.implicit_wait)
            self.driver.set_page_load_timeout(self.page_load_timeout)
            self.driver.set_script_timeout(self.script_timeout)
            
            self.logger.info(f"Successfully started Dolphin WebDriver for profile: {self.profile_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start Dolphin WebDriver: {e}")
            self.cleanup()
            return False
    
    def _create_driver(self):
        """Create the Chrome WebDriver instance with Dolphin profile connection"""
        try:
            # Create Chrome service with custom ChromeDriver
            service = Service(executable_path=self.chromedriver_path)
            
            # Create Chrome options
            options = Options()
            
            # Add debugging port to connect to the Dolphin profile
            if self.profile_data and self.profile_data.port:
                options.add_experimental_option("debuggerAddress", f"127.0.0.1:{self.profile_data.port}")
            
            # Additional options for better stealth
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Set a realistic user agent
            ua = UserAgent()
            options.add_argument(f"--user-agent={ua.random}")
            
            if self.headless:
                options.add_argument("--headless")
            
            # Create the WebDriver
            self.driver = webdriver.Chrome(service=service, options=options)
            
            # Execute script to remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
        except WebDriverException as e:
            raise DolphinDriverError(f"Failed to create WebDriver: {e}")
    
    def stop(self):
        """Stop the WebDriver and Dolphin profile"""
        self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        try:
            # Close WebDriver
            if self.driver:
                self.logger.info("Closing WebDriver")
                self.driver.quit()
                self.driver = None
            
            # Stop Dolphin profile
            if self.profile_id:
                self.logger.info(f"Stopping Dolphin profile: {self.profile_id}")
                self.dolphin_api.stop_profile(self.profile_id)
                
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def get_driver(self) -> webdriver.Chrome:
        """
        Get the WebDriver instance
        
        Returns:
            webdriver.Chrome: The Chrome WebDriver instance
            
        Raises:
            DolphinDriverError: If driver is not initialized
        """
        if not self.driver:
            raise DolphinDriverError("WebDriver not initialized. Call start() first.")
        return self.driver
    
    def wait_for_element(self, by: By, value: str, timeout: int = 10):
        """
        Wait for an element to be present and visible
        
        Args:
            by: Selenium By locator type
            value: Locator value
            timeout: Timeout in seconds
            
        Returns:
            WebElement: The found element
            
        Raises:
            TimeoutException: If element not found within timeout
        """
        if not self.driver:
            raise DolphinDriverError("WebDriver not initialized")
        
        wait = WebDriverWait(self.driver, timeout)
        return wait.until(EC.visibility_of_element_located((by, value)))
    
    def wait_for_clickable(self, by: By, value: str, timeout: int = 10):
        """
        Wait for an element to be clickable
        
        Args:
            by: Selenium By locator type
            value: Locator value
            timeout: Timeout in seconds
            
        Returns:
            WebElement: The clickable element
            
        Raises:
            TimeoutException: If element not clickable within timeout
        """
        if not self.driver:
            raise DolphinDriverError("WebDriver not initialized")
        
        wait = WebDriverWait(self.driver, timeout)
        return wait.until(EC.element_to_be_clickable((by, value)))
    
    def safe_click(self, by: By, value: str, timeout: int = 10) -> bool:
        """
        Safely click an element with wait and error handling
        
        Args:
            by: Selenium By locator type
            value: Locator value
            timeout: Timeout in seconds
            
        Returns:
            bool: True if click successful, False otherwise
        """
        try:
            element = self.wait_for_clickable(by, value, timeout)
            element.click()
            return True
        except TimeoutException:
            self.logger.error(f"Element not clickable within {timeout}s: {by}={value}")
            return False
        except Exception as e:
            self.logger.error(f"Error clicking element {by}={value}: {e}")
            return False
    
    def safe_send_keys(self, by: By, value: str, text: str, timeout: int = 10, clear: bool = True) -> bool:
        """
        Safely send keys to an element with wait and error handling
        
        Args:
            by: Selenium By locator type
            value: Locator value
            text: Text to send
            timeout: Timeout in seconds
            clear: Whether to clear the field first
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            element = self.wait_for_element(by, value, timeout)
            if clear:
                element.clear()
            element.send_keys(text)
            return True
        except TimeoutException:
            self.logger.error(f"Element not found within {timeout}s: {by}={value}")
            return False
        except Exception as e:
            self.logger.error(f"Error sending keys to element {by}={value}: {e}")
            return False
    
    def scroll_to_element(self, by: By, value: str) -> bool:
        """
        Scroll to an element
        
        Args:
            by: Selenium By locator type
            value: Locator value
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.driver:
                return False
            
            element = self.driver.find_element(by, value)
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)  # Small delay after scrolling
            return True
        except Exception as e:
            self.logger.error(f"Error scrolling to element {by}={value}: {e}")
            return False
    
    def __enter__(self):
        """Context manager entry"""
        if not self.start():
            raise DolphinDriverError("Failed to start Dolphin WebDriver")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()
