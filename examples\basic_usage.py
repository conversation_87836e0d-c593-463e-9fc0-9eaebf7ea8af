"""
Basic Usage Example

This example demonstrates how to use the Dolphin{anty} GMX Account Creator
to create a single GMX account.
"""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from ..src.dolphin_api import DolphinAP<PERSON>lient
from dolphin_driver import DolphinWebDriver
from gmx_creator import GMXAccountCreator
from utils import DataGenerator, setup_logging
from config.settings import settings


def main():
    """Basic usage example"""
    
    # Setup logging
    setup_logging(log_level="INFO")
    
    print("🚀 Dolphin{anty} GMX Account Creator - Basic Usage Example")
    print("=" * 60)
    
    # Validate settings
    if not settings.validate_all():
        print("❌ Settings validation failed. Please check your .env file.")
        return False
    
    # Check required settings
    if not settings.dolphin.profile_id:
        print("❌ No Dolphin profile ID configured. Please set DOLPHIN_PROFILE_ID in your .env file.")
        return False
    
    if not os.path.exists(settings.chromedriver.chromedriver_path):
        print(f"❌ ChromeDriver not found at: {settings.chromedriver.chromedriver_path}")
        print("Please download the custom ChromeDriver from:")
        print("https://anty-browser.s3.eu-central-1.amazonaws.com/chromedriver-138.zip")
        return False
    
    try:
        # Initialize Dolphin API client
        print("🔌 Connecting to Dolphin API...")
        dolphin_api = DolphinAPIClient(
            api_url=settings.dolphin.api_url,
            api_token=settings.dolphin.api_token
        )
        
        # Test API connection
        if not dolphin_api.health_check():
            print("❌ Cannot connect to Dolphin API. Make sure Dolphin{anty} is running.")
            return False
        
        print("✅ Connected to Dolphin API")
        
        # Authenticate if token is provided
        if settings.dolphin.api_token:
            if dolphin_api.authenticate():
                print("✅ Authenticated with Dolphin API")
            else:
                print("❌ Authentication failed")
                return False
        
        # Initialize data generator
        print("🎲 Initializing data generator...")
        data_generator = DataGenerator(
            first_names=settings.account.first_names,
            last_names=settings.account.last_names
        )
        
        # Generate person data
        person_data = data_generator.generate_person(
            min_age=settings.account.min_age,
            max_age=settings.account.max_age
        )
        
        print(f"👤 Generated person data:")
        print(f"   Name: {person_data.first_name} {person_data.last_name}")
        print(f"   Email: {person_data.email}")
        print(f"   Birth Date: {person_data.birth_date}")
        
        # Initialize Dolphin WebDriver
        print("🌐 Initializing Dolphin WebDriver...")
        dolphin_driver = DolphinWebDriver(
            profile_id=settings.dolphin.profile_id,
            chromedriver_path=settings.chromedriver.chromedriver_path,
            dolphin_api=dolphin_api,
            headless=settings.automation.headless,
            implicit_wait=settings.automation.implicit_wait,
            page_load_timeout=settings.automation.page_load_timeout,
            script_timeout=settings.automation.script_timeout
        )
        
        # Start the driver
        print("🚀 Starting Dolphin profile and WebDriver...")
        if not dolphin_driver.start():
            print("❌ Failed to start Dolphin WebDriver")
            return False
        
        print("✅ Dolphin WebDriver started successfully")
        
        try:
            # Initialize GMX Creator
            print("📧 Initializing GMX Account Creator...")
            gmx_creator = GMXAccountCreator(
                dolphin_driver=dolphin_driver,
                registration_url=settings.gmx.registration_url
            )
            
            # Create the account
            print("🔨 Creating GMX account...")
            print("This may take a few minutes...")
            
            result = gmx_creator.create_account(person_data, save_data=True)
            
            if result['success']:
                print("🎉 SUCCESS! GMX account created successfully!")
                print(f"   Email: {result['email']}")
                print(f"   Created at: {result['details'].get('created_at', 'Unknown')}")
                print("   Account data has been saved to the data/accounts/ directory")
                return True
            else:
                print("❌ FAILED to create GMX account")
                print(f"   Error: {result['error']}")
                return False
                
        finally:
            # Always cleanup
            print("🧹 Cleaning up...")
            dolphin_driver.cleanup()
            print("✅ Cleanup completed")
    
    except KeyboardInterrupt:
        print("\n⚠️  Operation interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎊 Example completed successfully!")
    else:
        print("\n💥 Example failed. Check the logs for more details.")
    
    input("\nPress Enter to exit...")
