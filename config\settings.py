"""
Configuration Management

This module handles all configuration settings for the Dolphin{anty} GMX account creator.
"""

import os
from pathlib import Path
from typing import Optional, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DolphinSettings:
    """Dolphin{anty} API configuration"""
    
    def __init__(self):
        self.api_token = os.getenv("DOLPHIN_API_TOKEN")
        self.api_url = os.getenv("DOLPHIN_API_URL", "http://localhost:3001").rstrip('/')
        self.profile_id = os.getenv("DOLPHIN_PROFILE_ID")
        
        # Validate API URL
        if not self.api_url.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')


class ChromeDriverSettings:
    """ChromeDriver configuration"""
    
    def __init__(self):
        self.chromedriver_path = os.getenv("CHROMEDRIVER_PATH", "./drivers/chromedriver.exe")
        
        # Convert to absolute path
        path = Path(self.chromedriver_path)
        if path.exists():
            self.chromedriver_path = str(path.absolute())


class GMXSettings:
    """GMX website configuration"""
    
    def __init__(self):
        self.registration_url = os.getenv("GMX_REGISTRATION_URL", "https://www.gmx.com/mail/registration/")
        
        # Validate URL
        if not self.registration_url.startswith(('http://', 'https://')):
            raise ValueError('Registration URL must start with http:// or https://')


class LoggingSettings:
    """Logging configuration"""
    
    def __init__(self):
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.log_file = os.getenv("LOG_FILE", "./logs/gmx_creator.log")
        self.log_format = os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        
        # Validate log level
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.log_level not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        
        # Ensure log directory exists
        log_path = Path(self.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        self.log_file = str(log_path.absolute())


class AutomationSettings:
    """Selenium automation configuration"""
    
    def __init__(self):
        self.implicit_wait = int(os.getenv("IMPLICIT_WAIT", "10"))
        self.page_load_timeout = int(os.getenv("PAGE_LOAD_TIMEOUT", "30"))
        self.script_timeout = int(os.getenv("SCRIPT_TIMEOUT", "30"))
        self.headless = os.getenv("HEADLESS", "False").lower() in ('true', '1', 'yes', 'on')
        
        # Delays and timing
        self.min_delay = float(os.getenv("MIN_DELAY", "1.0"))
        self.max_delay = float(os.getenv("MAX_DELAY", "3.0"))
        self.typing_delay = float(os.getenv("TYPING_DELAY", "0.1"))
        
        # Validate timeouts
        if self.implicit_wait <= 0 or self.page_load_timeout <= 0 or self.script_timeout <= 0:
            raise ValueError('Timeout values must be positive')
        
        # Validate delays
        if self.min_delay < 0 or self.max_delay < 0 or self.typing_delay < 0:
            raise ValueError('Delay values must be non-negative')
        
        if self.max_delay < self.min_delay:
            raise ValueError('max_delay must be greater than or equal to min_delay')


class AccountSettings:
    """Account generation settings"""
    
    def __init__(self):
        # Name lists
        self.first_names = [
            "John", "Jane", "Michael", "Sarah", "David", "Emma", "Robert", "Lisa",
            "James", "Maria", "William", "Jennifer", "Richard", "Patricia", "Charles",
            "Linda", "Joseph", "Elizabeth", "Thomas", "Barbara", "Christopher", "Susan"
        ]
        
        self.last_names = [
            "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller",
            "Davis", "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez",
            "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin"
        ]
        
        # Password settings
        self.password_length = int(os.getenv("PASSWORD_LENGTH", "12"))
        self.password_include_symbols = os.getenv("PASSWORD_INCLUDE_SYMBOLS", "True").lower() in ('true', '1', 'yes', 'on')
        self.password_include_numbers = os.getenv("PASSWORD_INCLUDE_NUMBERS", "True").lower() in ('true', '1', 'yes', 'on')
        self.password_include_uppercase = os.getenv("PASSWORD_INCLUDE_UPPERCASE", "True").lower() in ('true', '1', 'yes', 'on')
        self.password_include_lowercase = os.getenv("PASSWORD_INCLUDE_LOWERCASE", "True").lower() in ('true', '1', 'yes', 'on')
        
        # Age settings
        self.min_age = int(os.getenv("MIN_AGE", "18"))
        self.max_age = int(os.getenv("MAX_AGE", "65"))
        
        # Validate settings
        if self.password_length < 8:
            raise ValueError('Password length must be at least 8 characters')
        
        if self.min_age < 18 or self.max_age > 100:
            raise ValueError('Age must be between 18 and 100')
        
        if self.max_age < self.min_age:
            raise ValueError('max_age must be greater than or equal to min_age')


class Settings:
    """Main settings class that combines all configuration sections"""
    
    def __init__(self):
        self.dolphin = DolphinSettings()
        self.chromedriver = ChromeDriverSettings()
        self.gmx = GMXSettings()
        self.logging = LoggingSettings()
        self.automation = AutomationSettings()
        self.account = AccountSettings()
    
    def validate_all(self) -> bool:
        """
        Validate all settings
        
        Returns:
            bool: True if all settings are valid
        """
        try:
            # All validation is done in __init__ methods
            return True
        except Exception as e:
            print(f"Settings validation failed: {e}")
            return False
    
    def get_summary(self) -> dict:
        """
        Get a summary of current settings (excluding sensitive data)
        
        Returns:
            dict: Settings summary
        """
        return {
            "dolphin": {
                "api_url": self.dolphin.api_url,
                "has_token": bool(self.dolphin.api_token),
                "has_profile_id": bool(self.dolphin.profile_id)
            },
            "chromedriver": {
                "path": self.chromedriver.chromedriver_path
            },
            "gmx": {
                "registration_url": self.gmx.registration_url
            },
            "logging": {
                "level": self.logging.log_level,
                "file": self.logging.log_file
            },
            "automation": {
                "headless": self.automation.headless,
                "implicit_wait": self.automation.implicit_wait,
                "page_load_timeout": self.automation.page_load_timeout
            }
        }


# Global settings instance
settings = Settings()
