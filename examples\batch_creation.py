"""
Batch Account Creation Example

This example demonstrates how to create multiple GMX accounts in batch
with proper error handling and progress tracking.
"""

import sys
import os
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.dolphin_api import DolphinAPIClient
from src.dolphin_driver import DolphinWebDriver
from src.gmx_creator import GMXAccountCreator
from src.utils import DataGenerator, setup_logging
from config.settings import settings


def create_accounts_batch(count: int, delay_between: int = 60):
    """
    Create multiple GMX accounts in batch
    
    Args:
        count: Number of accounts to create
        delay_between: Delay between account creations in seconds
    """
    
    # Setup logging
    setup_logging(log_level="INFO")
    
    print(f"🚀 Batch GMX Account Creation - Creating {count} accounts")
    print("=" * 60)
    
    # Validate settings
    if not settings.validate_all():
        print("❌ Settings validation failed. Please check your .env file.")
        return
    
    # Check required settings
    if not settings.dolphin.profile_id:
        print("❌ No Dolphin profile ID configured.")
        return
    
    if not os.path.exists(settings.chromedriver.chromedriver_path):
        print(f"❌ ChromeDriver not found at: {settings.chromedriver.chromedriver_path}")
        return
    
    # Initialize components
    dolphin_api = DolphinAPIClient(
        api_url=settings.dolphin.api_url,
        api_token=settings.dolphin.api_token
    )
    
    # Test API connection
    if not dolphin_api.health_check():
        print("❌ Cannot connect to Dolphin API.")
        return
    
    # Authenticate if needed
    if settings.dolphin.api_token and not dolphin_api.authenticate():
        print("❌ Authentication failed")
        return
    
    # Initialize data generator
    data_generator = DataGenerator(
        first_names=settings.account.first_names,
        last_names=settings.account.last_names
    )
    
    # Track results
    results = {
        'successful': [],
        'failed': [],
        'total_time': 0
    }
    
    start_time = time.time()
    
    for i in range(count):
        account_start_time = time.time()
        
        print(f"\n📧 Creating account {i + 1}/{count}")
        print("-" * 40)
        
        try:
            # Generate person data
            person_data = data_generator.generate_person(
                min_age=settings.account.min_age,
                max_age=settings.account.max_age
            )
            
            print(f"👤 Generated: {person_data.first_name} {person_data.last_name} ({person_data.email})")
            
            # Initialize WebDriver for this account
            dolphin_driver = DolphinWebDriver(
                profile_id=settings.dolphin.profile_id,
                chromedriver_path=settings.chromedriver.chromedriver_path,
                dolphin_api=dolphin_api,
                headless=settings.automation.headless,
                implicit_wait=settings.automation.implicit_wait,
                page_load_timeout=settings.automation.page_load_timeout,
                script_timeout=settings.automation.script_timeout
            )
            
            # Start driver
            if not dolphin_driver.start():
                print("❌ Failed to start WebDriver")
                results['failed'].append({
                    'email': person_data.email,
                    'error': 'Failed to start WebDriver'
                })
                continue
            
            try:
                # Create GMX account
                gmx_creator = GMXAccountCreator(
                    dolphin_driver=dolphin_driver,
                    registration_url=settings.gmx.registration_url
                )
                
                result = gmx_creator.create_account(person_data, save_data=True)
                
                account_time = time.time() - account_start_time
                
                if result['success']:
                    print(f"✅ SUCCESS! Account created in {account_time:.1f}s")
                    results['successful'].append({
                        'email': result['email'],
                        'time': account_time
                    })
                else:
                    print(f"❌ FAILED: {result['error']}")
                    results['failed'].append({
                        'email': person_data.email,
                        'error': result['error']
                    })
                
            finally:
                # Always cleanup WebDriver
                dolphin_driver.cleanup()
            
            # Delay between accounts (except for the last one)
            if i < count - 1:
                print(f"⏳ Waiting {delay_between} seconds before next account...")
                time.sleep(delay_between)
                
        except KeyboardInterrupt:
            print("\n⚠️  Batch creation interrupted by user")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            results['failed'].append({
                'email': getattr(person_data, 'email', 'Unknown'),
                'error': str(e)
            })
            continue
    
    # Calculate total time
    results['total_time'] = time.time() - start_time
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 BATCH CREATION SUMMARY")
    print("=" * 60)
    print(f"Total accounts attempted: {count}")
    print(f"Successful: {len(results['successful'])}")
    print(f"Failed: {len(results['failed'])}")
    print(f"Success rate: {len(results['successful'])/count*100:.1f}%")
    print(f"Total time: {results['total_time']:.1f} seconds")
    
    if results['successful']:
        avg_time = sum(acc['time'] for acc in results['successful']) / len(results['successful'])
        print(f"Average time per successful account: {avg_time:.1f} seconds")
    
    # Show successful accounts
    if results['successful']:
        print(f"\n✅ SUCCESSFUL ACCOUNTS ({len(results['successful'])}):")
        for acc in results['successful']:
            print(f"   • {acc['email']} ({acc['time']:.1f}s)")
    
    # Show failed accounts
    if results['failed']:
        print(f"\n❌ FAILED ACCOUNTS ({len(results['failed'])}):")
        for acc in results['failed']:
            print(f"   • {acc['email']}: {acc['error']}")
    
    print("\n🎊 Batch creation completed!")


def main():
    """Main function with user input"""
    
    print("🚀 Dolphin{anty} GMX Account Creator - Batch Mode")
    print("=" * 50)
    
    try:
        # Get user input
        count = int(input("Enter number of accounts to create: "))
        if count <= 0:
            print("❌ Number must be positive")
            return
        
        delay = int(input("Enter delay between accounts in seconds (default 60): ") or "60")
        if delay < 0:
            print("❌ Delay must be non-negative")
            return
        
        # Confirm
        print(f"\n📋 Configuration:")
        print(f"   Accounts to create: {count}")
        print(f"   Delay between accounts: {delay} seconds")
        print(f"   Estimated total time: {(count * delay) / 60:.1f} minutes")
        
        confirm = input("\nProceed? (y/N): ").lower().strip()
        if confirm != 'y':
            print("Operation cancelled.")
            return
        
        # Start batch creation
        create_accounts_batch(count, delay)
        
    except ValueError:
        print("❌ Invalid input. Please enter numbers only.")
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")


if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
